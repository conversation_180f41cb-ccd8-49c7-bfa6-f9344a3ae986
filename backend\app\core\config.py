from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # App settings
    APP_NAME: str = "ccAI Platform"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Database
    DATABASE_URL: str = "postgresql://ccai_user:ccai_password@localhost:5432/ccai_db"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    
    # Kubernetes
    KUBERNETES_NAMESPACE: str = "ccai-models"
    KUBERNETES_CONFIG_PATH: Optional[str] = None  # None for in-cluster config
    
    # Docker Registry
    DOCKER_REGISTRY: str = "localhost:5000"
    
    # CORS
    BACKEND_CORS_ORIGINS: list = ["http://localhost:3000", "http://localhost:8005"]
    
    class Config:
        env_file = ".env"


settings = Settings()
