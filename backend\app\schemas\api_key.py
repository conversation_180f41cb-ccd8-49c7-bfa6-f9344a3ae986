from pydantic import BaseModel
from typing import Optional
from datetime import datetime


class APIKeyBase(BaseModel):
    name: str


class APIKeyCreate(APIKeyBase):
    pass


class APIKeyUpdate(BaseModel):
    name: Optional[str] = None
    is_active: Optional[bool] = None


class APIKeyInDB(APIKeyBase):
    id: int
    key_value: str
    user_id: int
    is_active: bool
    created_at: datetime
    last_used_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class APIKey(APIKeyInDB):
    pass


class APIKeyResponse(BaseModel):
    id: int
    name: str
    key_value: str
    is_active: bool
    created_at: datetime
    last_used_at: Optional[datetime] = None

    class Config:
        from_attributes = True
