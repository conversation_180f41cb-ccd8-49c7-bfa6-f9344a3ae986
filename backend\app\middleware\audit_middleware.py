from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response as StarletteResponse
import time
import json
from typing import Callable
from app.database import SessionLocal
from app.services.audit_service import AuditService
from app.schemas.audit_log import AuditLogCreate, ActionType, ResourceType, SeverityLevel
import re

class AuditMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, skip_paths: list = None):
        super().__init__(app)
        self.skip_paths = skip_paths or [
            "/docs", "/redoc", "/openapi.json", "/favicon.ico",
            "/health", "/metrics", "/static"
        ]
        
        # Define action mappings based on HTTP method and path patterns
        self.action_mappings = {
            "POST": {
                r"/api/v1/auth/login": ("LOGIN", "user"),
                r"/api/v1/auth/logout": ("LOGOUT", "user"),
                r"/api/v1/deployments": ("CREATE", "deployment"),
                r"/api/v1/usecases": ("CREATE", "usecase"),
                r"/api/v1/api-keys": ("CREATE", "api_key"),
                r"/api/v1/containers/deploy": ("DEPLOY", "container"),
            },
            "PUT": {
                r"/api/v1/deployments/(\w+)": ("UPDATE", "deployment"),
                r"/api/v1/usecases/(\w+)": ("UPDATE", "usecase"),
                r"/api/v1/containers/(\w+)/start": ("START", "container"),
                r"/api/v1/containers/(\w+)/stop": ("STOP", "container"),
                r"/api/v1/containers/(\w+)/configure": ("CONFIGURE", "container"),
            },
            "DELETE": {
                r"/api/v1/deployments/(\w+)": ("DELETE", "deployment"),
                r"/api/v1/usecases/(\w+)": ("DELETE", "usecase"),
                r"/api/v1/api-keys/(\w+)": ("DELETE", "api_key"),
            },
            "GET": {
                r"/api/v1/deployments": ("VIEW", "deployment"),
                r"/api/v1/usecases": ("VIEW", "usecase"),
                r"/api/v1/api-keys": ("VIEW", "api_key"),
                r"/api/v1/audit/logs": ("VIEW", "audit_log"),
            }
        }

    async def dispatch(self, request: Request, call_next: Callable) -> StarletteResponse:
        # Skip logging for certain paths
        if any(skip_path in str(request.url.path) for skip_path in self.skip_paths):
            return await call_next(request)
        
        start_time = time.time()
        
        # Get request body for POST/PUT requests
        request_body = None
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                body = await request.body()
                if body:
                    request_body = body.decode('utf-8')
                    # Re-create request with body for downstream processing
                    async def receive():
                        return {"type": "http.request", "body": body}
                    request._receive = receive
            except Exception:
                pass
        
        # Process the request
        response = await call_next(request)
        
        # Calculate response time
        process_time = int((time.time() - start_time) * 1000)
        
        # Log the action asynchronously
        try:
            await self._log_action(request, response, process_time, request_body)
        except Exception as e:
            # Don't fail the request if logging fails
            print(f"Audit logging failed: {str(e)}")
        
        return response

    async def _log_action(self, request: Request, response: Response, response_time: int, request_body: str = None):
        """Log the action to audit trail"""
        try:
            # Determine action and resource type
            action, resource_type = self._determine_action(request)
            
            if not action:
                return  # Skip if we can't determine the action
            
            # Extract user information from request
            user_id = None
            user_email = None
            session_id = None
            
            # Try to get user from authorization header or session
            auth_header = request.headers.get("authorization")
            if auth_header:
                # Extract user info from JWT token if needed
                # This would require JWT decoding logic
                pass
            
            # Extract resource ID from path
            resource_id = self._extract_resource_id(request.url.path)
            resource_name = self._extract_resource_name(request_body, response)
            
            # Determine module and feature
            module, feature = self._determine_module_feature(request.url.path)
            
            # Determine severity
            severity = self._determine_severity(response.status_code, action)
            
            # Prepare details
            details = {
                "path": str(request.url.path),
                "query_params": dict(request.query_params),
                "request_size": len(request_body) if request_body else 0,
                "response_size": response.headers.get("content-length", 0)
            }
            
            # Add request body for important actions (excluding sensitive data)
            if action in ["CREATE", "UPDATE", "CONFIGURE"] and request_body:
                try:
                    body_data = json.loads(request_body)
                    # Remove sensitive fields
                    sensitive_fields = ["password", "token", "secret", "key"]
                    filtered_body = {k: v for k, v in body_data.items() 
                                   if not any(field in k.lower() for field in sensitive_fields)}
                    details["request_data"] = filtered_body
                except:
                    pass
            
            # Create audit log entry
            audit_data = AuditLogCreate(
                user_id=user_id,
                user_email=user_email,
                action=ActionType(action),
                resource_type=ResourceType(resource_type),
                resource_id=resource_id,
                resource_name=resource_name,
                details=details,
                ip_address=request.client.host if request.client else None,
                user_agent=request.headers.get("user-agent"),
                session_id=session_id,
                method=request.method,
                endpoint=str(request.url.path),
                status_code=response.status_code,
                response_time_ms=response_time,
                module=module,
                feature=feature,
                severity=severity
            )
            
            # Save to database
            db = SessionLocal()
            try:
                audit_service = AuditService(db)
                audit_service.log_action(audit_data)
            finally:
                db.close()
                
        except Exception as e:
            print(f"Error in audit logging: {str(e)}")

    def _determine_action(self, request: Request) -> tuple:
        """Determine action and resource type from request"""
        method = request.method
        path = request.url.path
        
        if method in self.action_mappings:
            for pattern, (action, resource_type) in self.action_mappings[method].items():
                if re.match(pattern, path):
                    return action, resource_type
        
        return None, None

    def _extract_resource_id(self, path: str) -> str:
        """Extract resource ID from URL path"""
        # Look for UUID or numeric ID patterns
        import re
        
        # UUID pattern
        uuid_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        uuid_match = re.search(uuid_pattern, path)
        if uuid_match:
            return uuid_match.group()
        
        # Numeric ID pattern
        numeric_pattern = r'/(\d+)(?:/|$)'
        numeric_match = re.search(numeric_pattern, path)
        if numeric_match:
            return numeric_match.group(1)
        
        return None

    def _extract_resource_name(self, request_body: str, response: Response) -> str:
        """Extract resource name from request or response"""
        if request_body:
            try:
                data = json.loads(request_body)
                return data.get("name") or data.get("title") or data.get("deployment_name")
            except:
                pass
        return None

    def _determine_module_feature(self, path: str) -> tuple:
        """Determine module and feature from path"""
        if "/deployments" in path:
            return "CRM", "deployment_management"
        elif "/usecases" in path:
            return "CRM", "usecase_management"
        elif "/api-keys" in path:
            return "System", "api_key_management"
        elif "/containers" in path:
            return "CRM", "container_management"
        elif "/auth" in path:
            return "System", "authentication"
        elif "/audit" in path:
            return "System", "audit_management"
        else:
            return "System", "general"

    def _determine_severity(self, status_code: int, action: str) -> SeverityLevel:
        """Determine severity based on status code and action"""
        if status_code >= 500:
            return SeverityLevel.CRITICAL
        elif status_code >= 400:
            return SeverityLevel.ERROR
        elif action in ["DELETE", "LOGOUT"] or status_code >= 300:
            return SeverityLevel.WARNING
        else:
            return SeverityLevel.INFO
