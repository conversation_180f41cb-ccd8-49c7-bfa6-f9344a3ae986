{"ast": null, "code": "/**\n * lucide-react v0.264.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst HelpingHand = createLucideIcon(\"HelpingHand\", [[\"path\", {\n  d: \"m3 15 5.12-5.12A3 3 0 0 1 10.24 9H13a2 2 0 1 1 0 4h-2.5m4-.68 4.17-4.89a1.88 1.88 0 0 1 2.92 2.36l-4.2 5.94A3 3 0 0 1 14.96 17H9.83a2 2 0 0 0-1.42.59L7 19\",\n  key: \"nitrv7\"\n}], [\"path\", {\n  d: \"m2 14 6 6\",\n  key: \"g6j1uo\"\n}]]);\nexport { HelpingHand as default };", "map": {"version": 3, "names": ["HelpingHand", "createLucideIcon", "d", "key"], "sources": ["H:\\hoanganh\\Software\\Du_an\\sw_ccai\\frontend\\node_modules\\lucide-react\\src\\icons\\helping-hand.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name HelpingHand\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyAxNSA1LjEyLTUuMTJBMyAzIDAgMCAxIDEwLjI0IDlIMTNhMiAyIDAgMSAxIDAgNGgtMi41bTQtLjY4IDQuMTctNC44OWExLjg4IDEuODggMCAwIDEgMi45MiAyLjM2bC00LjIgNS45NEEzIDMgMCAwIDEgMTQuOTYgMTdIOS44M2EyIDIgMCAwIDAtMS40Mi41OUw3IDE5IiAvPgogIDxwYXRoIGQ9Im0yIDE0IDYgNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/helping-hand\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst HelpingHand = createLucideIcon('HelpingHand', [\n  [\n    'path',\n    {\n      d: 'm3 15 5.12-5.12A3 3 0 0 1 10.24 9H13a2 2 0 1 1 0 4h-2.5m4-.68 4.17-4.89a1.88 1.88 0 0 1 2.92 2.36l-4.2 5.94A3 3 0 0 1 14.96 17H9.83a2 2 0 0 0-1.42.59L7 19',\n      key: 'nitrv7',\n    },\n  ],\n  ['path', { d: 'm2 14 6 6', key: 'g6j1uo' }],\n]);\n\nexport default HelpingHand;\n"], "mappings": ";;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}