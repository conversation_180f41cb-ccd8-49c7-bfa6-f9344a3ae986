from sqlalchemy import Column, Integer, String, DateTime, Text, JSON
from sqlalchemy.sql import func
from app.database import Base

class AuditLog(Base):
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True)
    user_email = Column(String(255), index=True)
    action = Column(String(100), index=True)  # CREATE, UPDATE, DELETE, VIEW, LOGIN, LOGOUT
    resource_type = Column(String(50), index=True)  # deployment, usecase, api_key, user
    resource_id = Column(String(100), index=True)
    resource_name = Column(String(255))
    details = Column(JSON)  # Additional details about the action
    ip_address = Column(String(45))  # IPv4/IPv6
    user_agent = Column(Text)
    session_id = Column(String(255))
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # Request details
    method = Column(String(10))  # GET, POST, PUT, DELETE
    endpoint = Column(String(255))
    status_code = Column(Integer)
    response_time_ms = Column(Integer)
    
    # Additional context
    module = Column(String(50))  # CRM, HR, Sales, Analytics
    feature = Column(String(100))  # deployment_management, api_key_management
    severity = Column(String(20), default="INFO")  # INFO, WARNING, ERROR, CRITICAL
    
    def __repr__(self):
        return f"<AuditLog(id={self.id}, user={self.user_email}, action={self.action}, resource={self.resource_type})>"
