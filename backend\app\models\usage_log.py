from sqlalchemy import Column, Integer, String, DateTime, Foreign<PERSON>ey, Float, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..database import Base


class UsageLog(Base):
    __tablename__ = "usage_logs"

    id = Column(Integer, primary_key=True, index=True)
    usecase_id = Column(Integer, ForeignKey("usecases.id"))
    api_key_id = Column(Integer, ForeignKey("api_keys.id"))
    
    # Request info
    request_method = Column(String)
    request_path = Column(String)
    request_size = Column(Integer)  # bytes
    
    # Response info
    response_status = Column(Integer)
    response_size = Column(Integer)  # bytes
    response_time = Column(Float)  # seconds
    
    # Additional metadata
    request_metadata = Column(JSON)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    usecase = relationship("Usecase", back_populates="usage_logs")
    api_key = relationship("APIKey", back_populates="usage_logs")
