from sqlalchemy import Column, Inte<PERSON>, String, Text, <PERSON>olean, DateTime, ForeignKey, Enum, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from ..database import Base


class UsecaseStatus(enum.Enum):
    DRAFT = "draft"
    DEPLOYED = "deployed"
    STOPPED = "stopped"
    ERROR = "error"


class ModuleType(enum.Enum):
    CRM = "crm"
    HR = "hr"
    SALES = "sales"
    AI_ANALYTICS = "ai_analytics"
    API_MANAGER = "api_manager"
    CUSTOM = "custom"


class Usecase(Base):
    __tablename__ = "usecases"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    module_type = Column(Enum(ModuleType), nullable=False)
    docker_image = Column(String, nullable=False)
    status = Column(Enum(UsecaseStatus), default=UsecaseStatus.DRAFT)
    
    # Configuration
    config_params = Column(JSON)  # Store configuration as JSON
    endpoint_url = Column(String)  # K8s service endpoint
    
    # Kubernetes info
    k8s_deployment_name = Column(String)
    k8s_service_name = Column(String)
    k8s_namespace = Column(String)
    
    # Metadata
    created_by_id = Column(Integer, ForeignKey("users.id"))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    created_by = relationship("User", back_populates="usecases")
    usage_logs = relationship("UsageLog", back_populates="usecase")
