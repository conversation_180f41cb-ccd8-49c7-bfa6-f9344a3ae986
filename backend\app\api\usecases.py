from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Dict, Any
import httpx
from ..database import get_db
from ..models.user import User
from ..models.usecase import Usecase, UsecaseStatus
from ..schemas.usecase import UsecaseCreate, UsecaseUpdate, Usecase as UsecaseSchema, UsecaseDeployment, UsecaseSandbox
from ..api.auth import get_current_active_user
from ..services.kubernetes_service import k8s_service

router = APIRouter()


@router.get("/", response_model=List[UsecaseSchema])
def get_usecases(
    module_type: str = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all usecases for current user."""
    query = db.query(Usecase).filter(Usecase.created_by_id == current_user.id)
    
    if module_type:
        query = query.filter(Usecase.module_type == module_type)
    
    return query.all()


@router.post("/", response_model=UsecaseSchema)
def create_usecase(
    usecase: UsecaseCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new usecase."""
    db_usecase = Usecase(
        **usecase.dict(),
        created_by_id=current_user.id
    )
    db.add(db_usecase)
    db.commit()
    db.refresh(db_usecase)
    return db_usecase


@router.get("/{usecase_id}", response_model=UsecaseSchema)
def get_usecase(
    usecase_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific usecase."""
    usecase = db.query(Usecase).filter(
        Usecase.id == usecase_id,
        Usecase.created_by_id == current_user.id
    ).first()
    
    if not usecase:
        raise HTTPException(status_code=404, detail="Usecase not found")
    
    return usecase


@router.put("/{usecase_id}", response_model=UsecaseSchema)
def update_usecase(
    usecase_id: int,
    usecase_update: UsecaseUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a usecase."""
    usecase = db.query(Usecase).filter(
        Usecase.id == usecase_id,
        Usecase.created_by_id == current_user.id
    ).first()
    
    if not usecase:
        raise HTTPException(status_code=404, detail="Usecase not found")
    
    update_data = usecase_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(usecase, field, value)
    
    db.commit()
    db.refresh(usecase)
    return usecase


@router.delete("/{usecase_id}")
def delete_usecase(
    usecase_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a usecase."""
    usecase = db.query(Usecase).filter(
        Usecase.id == usecase_id,
        Usecase.created_by_id == current_user.id
    ).first()
    
    if not usecase:
        raise HTTPException(status_code=404, detail="Usecase not found")
    
    # Stop deployment if running
    if usecase.status == UsecaseStatus.DEPLOYED:
        try:
            k8s_service.delete_deployment(usecase.k8s_deployment_name, usecase.k8s_service_name)
        except Exception as e:
            # Log error but continue with deletion
            pass
    
    db.delete(usecase)
    db.commit()
    return {"message": "Usecase deleted successfully"}


@router.post("/{usecase_id}/deploy")
def deploy_usecase(
    usecase_id: int,
    deployment: UsecaseDeployment,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Deploy a usecase to Kubernetes."""
    usecase = db.query(Usecase).filter(
        Usecase.id == usecase_id,
        Usecase.created_by_id == current_user.id
    ).first()
    
    if not usecase:
        raise HTTPException(status_code=404, detail="Usecase not found")
    
    try:
        # Use provided config or fallback to usecase config
        config_params = deployment.config_params or usecase.config_params or {}
        
        # Deploy to Kubernetes
        k8s_info = k8s_service.create_deployment(
            usecase_id=usecase.id,
            docker_image=usecase.docker_image,
            config_params=config_params
        )
        
        # Update usecase with deployment info
        usecase.status = UsecaseStatus.DEPLOYED
        usecase.k8s_deployment_name = k8s_info["deployment_name"]
        usecase.k8s_service_name = k8s_info["service_name"]
        usecase.k8s_namespace = k8s_info["namespace"]
        usecase.endpoint_url = k8s_info["endpoint_url"]
        
        if deployment.config_params:
            usecase.config_params = deployment.config_params
        
        db.commit()
        db.refresh(usecase)
        
        return {
            "message": "Usecase deployed successfully",
            "endpoint_url": usecase.endpoint_url,
            "status": usecase.status
        }
        
    except Exception as e:
        usecase.status = UsecaseStatus.ERROR
        db.commit()
        raise HTTPException(status_code=500, detail=f"Deployment failed: {str(e)}")


@router.post("/{usecase_id}/stop")
def stop_usecase(
    usecase_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Stop a deployed usecase."""
    usecase = db.query(Usecase).filter(
        Usecase.id == usecase_id,
        Usecase.created_by_id == current_user.id
    ).first()
    
    if not usecase:
        raise HTTPException(status_code=404, detail="Usecase not found")
    
    if usecase.status != UsecaseStatus.DEPLOYED:
        raise HTTPException(status_code=400, detail="Usecase is not deployed")
    
    try:
        # Delete Kubernetes deployment
        success = k8s_service.delete_deployment(
            usecase.k8s_deployment_name,
            usecase.k8s_service_name
        )
        
        if success:
            usecase.status = UsecaseStatus.STOPPED
            usecase.endpoint_url = None
        else:
            usecase.status = UsecaseStatus.ERROR
        
        db.commit()
        db.refresh(usecase)
        
        return {
            "message": "Usecase stopped successfully",
            "status": usecase.status
        }
        
    except Exception as e:
        usecase.status = UsecaseStatus.ERROR
        db.commit()
        raise HTTPException(status_code=500, detail=f"Stop failed: {str(e)}")


@router.post("/{usecase_id}/sandbox")
async def test_usecase_sandbox(
    usecase_id: int,
    sandbox: UsecaseSandbox,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Test a usecase in sandbox mode."""
    usecase = db.query(Usecase).filter(
        Usecase.id == usecase_id,
        Usecase.created_by_id == current_user.id
    ).first()
    
    if not usecase:
        raise HTTPException(status_code=404, detail="Usecase not found")
    
    if usecase.status != UsecaseStatus.DEPLOYED:
        raise HTTPException(status_code=400, detail="Usecase must be deployed to test")
    
    try:
        # Send request to the deployed model
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{usecase.endpoint_url}/predict",
                json=sandbox.input_data,
                timeout=30.0
            )
            response.raise_for_status()
            
            return {
                "status": "success",
                "input": sandbox.input_data,
                "output": response.json(),
                "response_time": response.elapsed.total_seconds()
            }
            
    except httpx.RequestError as e:
        raise HTTPException(status_code=500, detail=f"Request failed: {str(e)}")
    except httpx.HTTPStatusError as e:
        raise HTTPException(status_code=e.response.status_code, detail=f"Model error: {e.response.text}")


@router.get("/{usecase_id}/status")
def get_usecase_status(
    usecase_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get real-time status of a usecase deployment."""
    usecase = db.query(Usecase).filter(
        Usecase.id == usecase_id,
        Usecase.created_by_id == current_user.id
    ).first()
    
    if not usecase:
        raise HTTPException(status_code=404, detail="Usecase not found")
    
    if usecase.status == UsecaseStatus.DEPLOYED and usecase.k8s_deployment_name:
        # Check real-time status from Kubernetes
        k8s_status = k8s_service.get_deployment_status(usecase.k8s_deployment_name)
        
        # Update status if needed
        if k8s_status == "not_found":
            usecase.status = UsecaseStatus.STOPPED
            db.commit()
        elif k8s_status == "error":
            usecase.status = UsecaseStatus.ERROR
            db.commit()
    
    return {
        "usecase_id": usecase.id,
        "status": usecase.status,
        "endpoint_url": usecase.endpoint_url,
        "deployment_name": usecase.k8s_deployment_name
    }
