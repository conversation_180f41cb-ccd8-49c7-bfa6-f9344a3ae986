from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
import secrets
import string
from datetime import datetime
from ..database import get_db
from ..models.user import User
from ..models.api_key import APIKey
from ..schemas.api_key import APIKeyCreate, APIKeyResponse, APIKeyUpdate
from ..api.auth import get_current_active_user

router = APIRouter()


def generate_api_key() -> str:
    """Generate a secure API key."""
    alphabet = string.ascii_letters + string.digits
    return "ccai_" + "".join(secrets.choice(alphabet) for _ in range(32))


@router.get("/", response_model=List[APIKeyResponse])
def get_api_keys(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all API keys for current user."""
    api_keys = db.query(APIKey).filter(APIKey.user_id == current_user.id).all()
    return api_keys


@router.post("/", response_model=APIKeyResponse)
def create_api_key(
    api_key: APIKeyCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new API key."""
    key_value = generate_api_key()
    
    db_api_key = APIKey(
        name=api_key.name,
        key_value=key_value,
        user_id=current_user.id
    )
    
    db.add(db_api_key)
    db.commit()
    db.refresh(db_api_key)
    
    return db_api_key


@router.get("/{api_key_id}", response_model=APIKeyResponse)
def get_api_key(
    api_key_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific API key."""
    api_key = db.query(APIKey).filter(
        APIKey.id == api_key_id,
        APIKey.user_id == current_user.id
    ).first()
    
    if not api_key:
        raise HTTPException(status_code=404, detail="API key not found")
    
    return api_key


@router.put("/{api_key_id}", response_model=APIKeyResponse)
def update_api_key(
    api_key_id: int,
    api_key_update: APIKeyUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update an API key."""
    api_key = db.query(APIKey).filter(
        APIKey.id == api_key_id,
        APIKey.user_id == current_user.id
    ).first()
    
    if not api_key:
        raise HTTPException(status_code=404, detail="API key not found")
    
    update_data = api_key_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(api_key, field, value)
    
    db.commit()
    db.refresh(api_key)
    return api_key


@router.delete("/{api_key_id}")
def delete_api_key(
    api_key_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete an API key."""
    api_key = db.query(APIKey).filter(
        APIKey.id == api_key_id,
        APIKey.user_id == current_user.id
    ).first()
    
    if not api_key:
        raise HTTPException(status_code=404, detail="API key not found")
    
    db.delete(api_key)
    db.commit()
    return {"message": "API key deleted successfully"}


@router.post("/{api_key_id}/regenerate", response_model=APIKeyResponse)
def regenerate_api_key(
    api_key_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Regenerate an API key."""
    api_key = db.query(APIKey).filter(
        APIKey.id == api_key_id,
        APIKey.user_id == current_user.id
    ).first()
    
    if not api_key:
        raise HTTPException(status_code=404, detail="API key not found")
    
    # Generate new key
    api_key.key_value = generate_api_key()
    api_key.last_used_at = None  # Reset usage timestamp
    
    db.commit()
    db.refresh(api_key)
    return api_key


def get_api_key_by_value(db: Session, key_value: str) -> APIKey:
    """Get API key by its value."""
    return db.query(APIKey).filter(
        APIKey.key_value == key_value,
        APIKey.is_active == True
    ).first()


def update_api_key_usage(db: Session, api_key: APIKey):
    """Update API key last used timestamp."""
    api_key.last_used_at = datetime.utcnow()
    db.commit()
