from kubernetes import client, config
from kubernetes.client.rest import ApiException
from typing import Dict, Any, Optional
import logging
from ..core.config import settings

logger = logging.getLogger(__name__)


class KubernetesService:
    def __init__(self):
        self.enabled = False
        try:
            # Try to load in-cluster config first
            if settings.KUBERNETES_CONFIG_PATH:
                config.load_kube_config(config_file=settings.KUBERNETES_CONFIG_PATH)
            else:
                try:
                    config.load_incluster_config()
                except:
                    # Fallback to local config
                    config.load_kube_config()

            self.apps_v1 = client.AppsV1Api()
            self.core_v1 = client.CoreV1Api()
            self.namespace = settings.KUBERNETES_NAMESPACE
            self.enabled = True
            logger.info("Kubernetes client initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize Kubernetes client: {e}")
            logger.warning("Kubernetes features will be disabled")
            self.apps_v1 = None
            self.core_v1 = None
            self.namespace = None

    def create_deployment(self, usecase_id: int, docker_image: str, config_params: Dict[str, Any]) -> Dict[str, str]:
        """Create a Kubernetes deployment for a usecase."""
        if not self.enabled:
            logger.warning("Kubernetes is not available, returning mock deployment info")
            return {
                "deployment_name": f"usecase-{usecase_id}",
                "service_name": f"usecase-{usecase_id}-service",
                "endpoint_url": f"http://localhost:8080/usecase-{usecase_id}",
                "status": "mock_deployed"
            }

        deployment_name = f"usecase-{usecase_id}"
        service_name = f"usecase-{usecase_id}-service"
        
        # Create deployment
        deployment = self._create_deployment_manifest(deployment_name, docker_image, config_params)
        
        try:
            # Create deployment
            self.apps_v1.create_namespaced_deployment(
                namespace=self.namespace,
                body=deployment
            )
            
            # Create service
            service = self._create_service_manifest(service_name, deployment_name)
            self.core_v1.create_namespaced_service(
                namespace=self.namespace,
                body=service
            )
            
            # Get service endpoint
            endpoint_url = f"http://{service_name}.{self.namespace}.svc.cluster.local:80"
            
            return {
                "deployment_name": deployment_name,
                "service_name": service_name,
                "namespace": self.namespace,
                "endpoint_url": endpoint_url
            }
            
        except ApiException as e:
            logger.error(f"Failed to create deployment: {e}")
            raise Exception(f"Kubernetes deployment failed: {e}")

    def delete_deployment(self, deployment_name: str, service_name: str) -> bool:
        """Delete a Kubernetes deployment and service."""
        try:
            # Delete deployment
            self.apps_v1.delete_namespaced_deployment(
                name=deployment_name,
                namespace=self.namespace
            )
            
            # Delete service
            self.core_v1.delete_namespaced_service(
                name=service_name,
                namespace=self.namespace
            )
            
            return True
            
        except ApiException as e:
            logger.error(f"Failed to delete deployment: {e}")
            return False

    def get_deployment_status(self, deployment_name: str) -> str:
        """Get the status of a deployment."""
        try:
            deployment = self.apps_v1.read_namespaced_deployment(
                name=deployment_name,
                namespace=self.namespace
            )
            
            if deployment.status.ready_replicas and deployment.status.ready_replicas > 0:
                return "running"
            else:
                return "pending"
                
        except ApiException as e:
            if e.status == 404:
                return "not_found"
            logger.error(f"Failed to get deployment status: {e}")
            return "error"

    def _create_deployment_manifest(self, name: str, image: str, config_params: Dict[str, Any]):
        """Create deployment manifest."""
        env_vars = []
        for key, value in config_params.items():
            env_vars.append(client.V1EnvVar(name=key.upper(), value=str(value)))
        
        container = client.V1Container(
            name=name,
            image=image,
            ports=[client.V1ContainerPort(container_port=8000)],
            env=env_vars
        )
        
        template = client.V1PodTemplateSpec(
            metadata=client.V1ObjectMeta(labels={"app": name}),
            spec=client.V1PodSpec(containers=[container])
        )
        
        spec = client.V1DeploymentSpec(
            replicas=1,
            selector=client.V1LabelSelector(match_labels={"app": name}),
            template=template
        )
        
        deployment = client.V1Deployment(
            api_version="apps/v1",
            kind="Deployment",
            metadata=client.V1ObjectMeta(name=name),
            spec=spec
        )
        
        return deployment

    def _create_service_manifest(self, service_name: str, deployment_name: str):
        """Create service manifest."""
        service = client.V1Service(
            api_version="v1",
            kind="Service",
            metadata=client.V1ObjectMeta(name=service_name),
            spec=client.V1ServiceSpec(
                selector={"app": deployment_name},
                ports=[client.V1ServicePort(port=80, target_port=8000)],
                type="ClusterIP"
            )
        )
        
        return service


# Singleton instance
k8s_service = KubernetesService()
