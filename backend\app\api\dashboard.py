from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import Dict, Any, List
from ..database import get_db
from ..models.user import User
from ..models.usecase import Usecase, ModuleType, UsecaseStatus
from ..models.usage_log import UsageLog
from ..api.auth import get_current_active_user

router = APIRouter()


@router.get("/stats")
def get_dashboard_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Get dashboard statistics."""
    
    # Total usecases by user
    total_usecases = db.query(Usecase).filter(Usecase.created_by_id == current_user.id).count()
    
    # Active usecases
    active_usecases = db.query(Usecase).filter(
        Usecase.created_by_id == current_user.id,
        Usecase.status == UsecaseStatus.DEPLOYED
    ).count()
    
    # Total API calls (last 30 days)
    total_api_calls = db.query(UsageLog).join(Usecase).filter(
        Usecase.created_by_id == current_user.id
    ).count()
    
    # Usecases by module
    usecases_by_module = db.query(
        Usecase.module_type,
        func.count(Usecase.id).label('count')
    ).filter(
        Usecase.created_by_id == current_user.id
    ).group_by(Usecase.module_type).all()
    
    module_stats = {module.value: 0 for module in ModuleType}
    for module_type, count in usecases_by_module:
        module_stats[module_type.value] = count
    
    return {
        "total_usecases": total_usecases,
        "active_usecases": active_usecases,
        "total_api_calls": total_api_calls,
        "usecases_by_module": module_stats
    }


@router.get("/modules")
def get_modules(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> List[Dict[str, Any]]:
    """Get available modules with usecase counts."""
    
    modules = []
    for module_type in ModuleType:
        usecase_count = db.query(Usecase).filter(
            Usecase.created_by_id == current_user.id,
            Usecase.module_type == module_type
        ).count()
        
        active_count = db.query(Usecase).filter(
            Usecase.created_by_id == current_user.id,
            Usecase.module_type == module_type,
            Usecase.status == UsecaseStatus.DEPLOYED
        ).count()
        
        modules.append({
            "type": module_type.value,
            "name": module_type.value.replace("_", " ").title(),
            "description": get_module_description(module_type),
            "icon": get_module_icon(module_type),
            "usecase_count": usecase_count,
            "active_count": active_count
        })
    
    return modules


def get_module_description(module_type: ModuleType) -> str:
    """Get description for module type."""
    descriptions = {
        ModuleType.CRM: "Customer Relationship Management AI tools",
        ModuleType.HR: "Human Resources AI automation",
        ModuleType.SALES: "Sales optimization and forecasting",
        ModuleType.AI_ANALYTICS: "Advanced analytics and insights",
        ModuleType.API_MANAGER: "API management and monitoring",
        ModuleType.CUSTOM: "Custom AI solutions"
    }
    return descriptions.get(module_type, "AI-powered solutions")


def get_module_icon(module_type: ModuleType) -> str:
    """Get icon for module type."""
    icons = {
        ModuleType.CRM: "users",
        ModuleType.HR: "user-check",
        ModuleType.SALES: "trending-up",
        ModuleType.AI_ANALYTICS: "bar-chart",
        ModuleType.API_MANAGER: "settings",
        ModuleType.CUSTOM: "puzzle"
    }
    return icons.get(module_type, "box")
