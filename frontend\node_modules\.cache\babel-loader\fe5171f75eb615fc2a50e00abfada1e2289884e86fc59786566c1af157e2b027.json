{"ast": null, "code": "var _jsxFileName = \"H:\\\\hoanganh\\\\Software\\\\Du_an\\\\sw_ccai\\\\frontend\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { dashboardAPI } from '../services/api';\nimport { Users, UserCheck, TrendingUp, BarChart, Settings, Puzzle, Activity, Zap, Clock } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [stats, setStats] = useState(null);\n  const [modules, setModules] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const loadDashboardData = async () => {\n    try {\n      const [statsResponse, modulesResponse] = await Promise.all([dashboardAPI.getStats(), dashboardAPI.getModules()]);\n      setStats(statsResponse.data);\n      setModules(modulesResponse.data);\n    } catch (error) {\n      toast.error('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getModuleIcon = type => {\n    const icons = {\n      crm: Users,\n      hr: UserCheck,\n      sales: TrendingUp,\n      ai_analytics: BarChart,\n      api_manager: Settings,\n      custom: Puzzle\n    };\n    return icons[type] || Puzzle;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-900\",\n        children: \"Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-2 text-gray-600\",\n        children: \"Welcome to ccAI Platform - Manage your AI usecases\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(Puzzle, {\n              className: \"h-8 w-8 text-primary-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Total Usecases\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-semibold text-gray-900\",\n              children: (stats === null || stats === void 0 ? void 0 : stats.total_usecases) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(Activity, {\n              className: \"h-8 w-8 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Active Usecases\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-semibold text-gray-900\",\n              children: (stats === null || stats === void 0 ? void 0 : stats.active_usecases) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(Zap, {\n              className: \"h-8 w-8 text-yellow-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"API Calls\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-semibold text-gray-900\",\n              children: (stats === null || stats === void 0 ? void 0 : stats.total_api_calls) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(Clock, {\n              className: \"h-8 w-8 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Avg Response\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-semibold text-gray-900\",\n              children: \"~150ms\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-gray-900 mb-6\",\n        children: \"AI Modules\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: modules.map(module => {\n          const IconComponent = getModuleIcon(module.type);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer border border-gray-200 hover:border-primary-300\",\n            onClick: () => window.location.href = `/modules/${module.type}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                      className: \"h-8 w-8 text-primary-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-medium text-gray-900\",\n                      children: module.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 149,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                    children: [module.usecase_count, \" usecases\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                    children: [module.active_count, \" active\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 mb-4\",\n                children: module.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"text-primary-600 hover:text-primary-700 text-sm font-medium\",\n                  children: \"View Usecases \\u2192\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this)\n          }, module.type, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(Puzzle, {\n            className: \"h-5 w-5 mr-2 text-primary-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), \"Create New Usecase\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(Settings, {\n            className: \"h-5 w-5 mr-2 text-primary-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), \"Manage API Keys\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(BarChart, {\n            className: \"h-5 w-5 mr-2 text-primary-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), \"View Analytics\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"aWqKi4zVDRNjG4JPHDAl4jSRzKM=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "dashboardAPI", "Users", "UserCheck", "TrendingUp", "<PERSON><PERSON><PERSON>", "Settings", "Puzzle", "Activity", "Zap", "Clock", "toast", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "stats", "setStats", "modules", "setModules", "loading", "setLoading", "loadDashboardData", "statsResponse", "modulesResponse", "Promise", "all", "getStats", "getModules", "data", "error", "getModuleIcon", "type", "icons", "crm", "hr", "sales", "ai_analytics", "api_manager", "custom", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "total_usecases", "active_usecases", "total_api_calls", "map", "module", "IconComponent", "onClick", "window", "location", "href", "name", "usecase_count", "active_count", "description", "_c", "$RefreshReg$"], "sources": ["H:/hoanganh/Software/Du_an/sw_ccai/frontend/src/components/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { dashboardAPI } from '../services/api';\nimport { \n  Users, \n  UserCheck, \n  TrendingUp, \n  Bar<PERSON>hart, \n  Settings, \n  Puzzle,\n  Activity,\n  Zap,\n  Clock\n} from 'lucide-react';\nimport toast from 'react-hot-toast';\n\nconst Dashboard = () => {\n  const [stats, setStats] = useState(null);\n  const [modules, setModules] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      const [statsResponse, modulesResponse] = await Promise.all([\n        dashboardAPI.getStats(),\n        dashboardAPI.getModules()\n      ]);\n      \n      setStats(statsResponse.data);\n      setModules(modulesResponse.data);\n    } catch (error) {\n      toast.error('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getModuleIcon = (type) => {\n    const icons = {\n      crm: Users,\n      hr: UserCheck,\n      sales: TrendingUp,\n      ai_analytics: Bar<PERSON><PERSON>,\n      api_manager: Settings,\n      custom: Puzzle,\n    };\n    return icons[type] || Puzzle;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\n        <p className=\"mt-2 text-gray-600\">\n          Welcome to ccAI Platform - Manage your AI usecases\n        </p>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <Puzzle className=\"h-8 w-8 text-primary-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">Total Usecases</p>\n              <p className=\"text-2xl font-semibold text-gray-900\">\n                {stats?.total_usecases || 0}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <Activity className=\"h-8 w-8 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">Active Usecases</p>\n              <p className=\"text-2xl font-semibold text-gray-900\">\n                {stats?.active_usecases || 0}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <Zap className=\"h-8 w-8 text-yellow-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">API Calls</p>\n              <p className=\"text-2xl font-semibold text-gray-900\">\n                {stats?.total_api_calls || 0}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <Clock className=\"h-8 w-8 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">Avg Response</p>\n              <p className=\"text-2xl font-semibold text-gray-900\">\n                ~150ms\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Modules Grid */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">AI Modules</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {modules.map((module) => {\n            const IconComponent = getModuleIcon(module.type);\n            return (\n              <div\n                key={module.type}\n                className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer border border-gray-200 hover:border-primary-300\"\n                onClick={() => window.location.href = `/modules/${module.type}`}\n              >\n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0\">\n                        <IconComponent className=\"h-8 w-8 text-primary-600\" />\n                      </div>\n                      <div className=\"ml-3\">\n                        <h3 className=\"text-lg font-medium text-gray-900\">\n                          {module.name}\n                        </h3>\n                      </div>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n                        {module.usecase_count} usecases\n                      </span>\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                        {module.active_count} active\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <p className=\"text-sm text-gray-600 mb-4\">\n                    {module.description}\n                  </p>\n                  \n                  <div className=\"flex justify-between items-center\">\n                    <button className=\"text-primary-600 hover:text-primary-700 text-sm font-medium\">\n                      View Usecases →\n                    </button>\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Quick Actions</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <button className=\"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\">\n            <Puzzle className=\"h-5 w-5 mr-2 text-primary-600\" />\n            Create New Usecase\n          </button>\n          <button className=\"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\">\n            <Settings className=\"h-5 w-5 mr-2 text-primary-600\" />\n            Manage API Keys\n          </button>\n          <button className=\"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\">\n            <BarChart className=\"h-5 w-5 mr-2 text-primary-600\" />\n            View Analytics\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SACEC,KAAK,EACLC,SAAS,EACTC,UAAU,EACVC,QAAQ,EACRC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,KAAK,QACA,cAAc;AACrB,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdsB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAM,CAACC,aAAa,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACzDzB,YAAY,CAAC0B,QAAQ,CAAC,CAAC,EACvB1B,YAAY,CAAC2B,UAAU,CAAC,CAAC,CAC1B,CAAC;MAEFX,QAAQ,CAACM,aAAa,CAACM,IAAI,CAAC;MAC5BV,UAAU,CAACK,eAAe,CAACK,IAAI,CAAC;IAClC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdnB,KAAK,CAACmB,KAAK,CAAC,+BAA+B,CAAC;IAC9C,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,aAAa,GAAIC,IAAI,IAAK;IAC9B,MAAMC,KAAK,GAAG;MACZC,GAAG,EAAEhC,KAAK;MACViC,EAAE,EAAEhC,SAAS;MACbiC,KAAK,EAAEhC,UAAU;MACjBiC,YAAY,EAAEhC,QAAQ;MACtBiC,WAAW,EAAEhC,QAAQ;MACrBiC,MAAM,EAAEhC;IACV,CAAC;IACD,OAAO0B,KAAK,CAACD,IAAI,CAAC,IAAIzB,MAAM;EAC9B,CAAC;EAED,IAAIa,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK2B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD5B,OAAA;QAAK2B,SAAS,EAAC;MAAiE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF,CAAC;EAEV;EAEA,oBACEhC,OAAA;IAAK2B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB5B,OAAA;MAAA4B,QAAA,gBACE5B,OAAA;QAAI2B,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/DhC,OAAA;QAAG2B,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNhC,OAAA;MAAK2B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnE5B,OAAA;QAAK2B,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C5B,OAAA;UAAK2B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5B,OAAA;YAAK2B,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B5B,OAAA,CAACN,MAAM;cAACiC,SAAS,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACNhC,OAAA;YAAK2B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB5B,OAAA;cAAG2B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnEhC,OAAA;cAAG2B,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAChD,CAAAzB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE8B,cAAc,KAAI;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhC,OAAA;QAAK2B,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C5B,OAAA;UAAK2B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5B,OAAA;YAAK2B,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B5B,OAAA,CAACL,QAAQ;cAACgC,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACNhC,OAAA;YAAK2B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB5B,OAAA;cAAG2B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpEhC,OAAA;cAAG2B,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAChD,CAAAzB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE+B,eAAe,KAAI;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhC,OAAA;QAAK2B,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C5B,OAAA;UAAK2B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5B,OAAA;YAAK2B,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B5B,OAAA,CAACJ,GAAG;cAAC+B,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACNhC,OAAA;YAAK2B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB5B,OAAA;cAAG2B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9DhC,OAAA;cAAG2B,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAChD,CAAAzB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgC,eAAe,KAAI;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhC,OAAA;QAAK2B,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C5B,OAAA;UAAK2B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5B,OAAA;YAAK2B,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B5B,OAAA,CAACH,KAAK;cAAC8B,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACNhC,OAAA;YAAK2B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB5B,OAAA;cAAG2B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjEhC,OAAA;cAAG2B,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhC,OAAA;MAAA4B,QAAA,gBACE5B,OAAA;QAAI2B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrEhC,OAAA;QAAK2B,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEvB,OAAO,CAAC+B,GAAG,CAAEC,MAAM,IAAK;UACvB,MAAMC,aAAa,GAAGpB,aAAa,CAACmB,MAAM,CAAClB,IAAI,CAAC;UAChD,oBACEnB,OAAA;YAEE2B,SAAS,EAAC,6HAA6H;YACvIY,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAYL,MAAM,CAAClB,IAAI,EAAG;YAAAS,QAAA,eAEhE5B,OAAA;cAAK2B,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClB5B,OAAA;gBAAK2B,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD5B,OAAA;kBAAK2B,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC5B,OAAA;oBAAK2B,SAAS,EAAC,eAAe;oBAAAC,QAAA,eAC5B5B,OAAA,CAACsC,aAAa;sBAACX,SAAS,EAAC;oBAA0B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNhC,OAAA;oBAAK2B,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnB5B,OAAA;sBAAI2B,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC9CS,MAAM,CAACM;oBAAI;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhC,OAAA;kBAAK2B,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B5B,OAAA;oBAAM2B,SAAS,EAAC,mGAAmG;oBAAAC,QAAA,GAChHS,MAAM,CAACO,aAAa,EAAC,WACxB;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPhC,OAAA;oBAAM2B,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,GAClHS,MAAM,CAACQ,YAAY,EAAC,SACvB;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhC,OAAA;gBAAG2B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EACtCS,MAAM,CAACS;cAAW;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eAEJhC,OAAA;gBAAK2B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAChD5B,OAAA;kBAAQ2B,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EAAC;gBAEhF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAnCDK,MAAM,CAAClB,IAAI;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoCb,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhC,OAAA;MAAK2B,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7C5B,OAAA;QAAI2B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzEhC,OAAA;QAAK2B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD5B,OAAA;UAAQ2B,SAAS,EAAC,oJAAoJ;UAAAC,QAAA,gBACpK5B,OAAA,CAACN,MAAM;YAACiC,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThC,OAAA;UAAQ2B,SAAS,EAAC,oJAAoJ;UAAAC,QAAA,gBACpK5B,OAAA,CAACP,QAAQ;YAACkC,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAExD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThC,OAAA;UAAQ2B,SAAS,EAAC,oJAAoJ;UAAAC,QAAA,gBACpK5B,OAAA,CAACR,QAAQ;YAACmC,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAExD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAxLID,SAAS;AAAA8C,EAAA,GAAT9C,SAAS;AA0Lf,eAAeA,SAAS;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}