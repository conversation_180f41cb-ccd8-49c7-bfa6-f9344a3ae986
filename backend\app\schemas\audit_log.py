from pydantic import BaseModel
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum

class ActionType(str, Enum):
    CREATE = "CREATE"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    VIEW = "VIEW"
    LOGIN = "LOGIN"
    LOGOUT = "LOGOUT"
    DEPLOY = "DEPLOY"
    START = "START"
    STOP = "STOP"
    CONFIGURE = "CONFIGURE"
    DOWNLOAD = "DOWNLOAD"
    UPLOAD = "UPLOAD"

class ResourceType(str, Enum):
    DEPLOYMENT = "deployment"
    USECASE = "usecase"
    API_KEY = "api_key"
    USER = "user"
    CONTAINER = "container"
    IMAGE = "image"
    SYSTEM = "system"

class SeverityLevel(str, Enum):
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class AuditLogCreate(BaseModel):
    user_id: Optional[int] = None
    user_email: Optional[str] = None
    action: ActionType
    resource_type: ResourceType
    resource_id: Optional[str] = None
    resource_name: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    session_id: Optional[str] = None
    method: Optional[str] = None
    endpoint: Optional[str] = None
    status_code: Optional[int] = None
    response_time_ms: Optional[int] = None
    module: Optional[str] = None
    feature: Optional[str] = None
    severity: SeverityLevel = SeverityLevel.INFO

class AuditLogResponse(BaseModel):
    id: int
    user_id: Optional[int]
    user_email: Optional[str]
    action: str
    resource_type: str
    resource_id: Optional[str]
    resource_name: Optional[str]
    details: Optional[Dict[str, Any]]
    ip_address: Optional[str]
    user_agent: Optional[str]
    session_id: Optional[str]
    timestamp: datetime
    method: Optional[str]
    endpoint: Optional[str]
    status_code: Optional[int]
    response_time_ms: Optional[int]
    module: Optional[str]
    feature: Optional[str]
    severity: str

    class Config:
        from_attributes = True

class AuditLogFilter(BaseModel):
    user_id: Optional[int] = None
    user_email: Optional[str] = None
    action: Optional[ActionType] = None
    resource_type: Optional[ResourceType] = None
    resource_id: Optional[str] = None
    module: Optional[str] = None
    feature: Optional[str] = None
    severity: Optional[SeverityLevel] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    ip_address: Optional[str] = None
    limit: int = 100
    offset: int = 0
