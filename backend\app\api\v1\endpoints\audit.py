from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from typing import List
from app.database import get_db
from app.services.audit_service import AuditService
from app.schemas.audit_log import Audit<PERSON>og<PERSON><PERSON>, AuditLogResponse, AuditLogFilter
from app.core.auth import get_current_user
from app.models.user import User

router = APIRouter()

@router.post("/log", response_model=dict)
async def create_audit_log(
    request: Request,
    audit_data: AuditLogCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create an audit log entry"""
    try:
        audit_service = AuditService(db)
        
        # Enrich audit data with request information
        if not audit_data.user_id and current_user:
            audit_data.user_id = current_user.id
        if not audit_data.user_email and current_user:
            audit_data.user_email = current_user.email
        if not audit_data.ip_address:
            audit_data.ip_address = request.client.host
        if not audit_data.user_agent:
            audit_data.user_agent = request.headers.get("user-agent")
        
        audit_log = audit_service.log_action(audit_data)
        
        if audit_log:
            return {"success": True, "id": audit_log.id}
        else:
            return {"success": False, "message": "Failed to create audit log"}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create audit log: {str(e)}")

@router.get("/logs", response_model=List[AuditLogResponse])
async def get_audit_logs(
    user_id: int = None,
    user_email: str = None,
    action: str = None,
    resource_type: str = None,
    resource_id: str = None,
    module: str = None,
    feature: str = None,
    severity: str = None,
    start_date: str = None,
    end_date: str = None,
    ip_address: str = None,
    limit: int = 100,
    offset: int = 0,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get audit logs with filtering"""
    try:
        from datetime import datetime
        
        # Parse dates if provided
        start_dt = None
        end_dt = None
        if start_date:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        if end_date:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        filters = AuditLogFilter(
            user_id=user_id,
            user_email=user_email,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            module=module,
            feature=feature,
            severity=severity,
            start_date=start_dt,
            end_date=end_dt,
            ip_address=ip_address,
            limit=min(limit, 1000),  # Cap at 1000
            offset=offset
        )
        
        audit_service = AuditService(db)
        logs = audit_service.get_audit_logs(filters)
        
        return logs
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get audit logs: {str(e)}")

@router.get("/user/{user_id}/activity", response_model=List[AuditLogResponse])
async def get_user_activity(
    user_id: int,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get recent activity for a specific user"""
    try:
        audit_service = AuditService(db)
        logs = audit_service.get_user_activity(user_id, limit)
        return logs
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get user activity: {str(e)}")

@router.get("/resource/{resource_type}/{resource_id}/history", response_model=List[AuditLogResponse])
async def get_resource_history(
    resource_type: str,
    resource_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get history for a specific resource"""
    try:
        audit_service = AuditService(db)
        logs = audit_service.get_resource_history(resource_type, resource_id)
        return logs
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get resource history: {str(e)}")

@router.get("/security-events", response_model=List[AuditLogResponse])
async def get_security_events(
    hours: int = 24,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get security-related events in the last N hours"""
    try:
        audit_service = AuditService(db)
        logs = audit_service.get_security_events(hours)
        return logs
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get security events: {str(e)}")

@router.get("/stats", response_model=dict)
async def get_audit_stats(
    hours: int = 24,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get audit statistics"""
    try:
        from datetime import datetime, timedelta
        from sqlalchemy import func
        from app.models.audit_log import AuditLog
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        # Total actions in time period
        total_actions = db.query(func.count(AuditLog.id))\
            .filter(AuditLog.timestamp >= cutoff_time)\
            .scalar()
        
        # Actions by type
        actions_by_type = db.query(AuditLog.action, func.count(AuditLog.id))\
            .filter(AuditLog.timestamp >= cutoff_time)\
            .group_by(AuditLog.action)\
            .all()
        
        # Top users by activity
        top_users = db.query(AuditLog.user_email, func.count(AuditLog.id))\
            .filter(AuditLog.timestamp >= cutoff_time)\
            .filter(AuditLog.user_email.isnot(None))\
            .group_by(AuditLog.user_email)\
            .order_by(func.count(AuditLog.id).desc())\
            .limit(10)\
            .all()
        
        # Security events count
        security_events = db.query(func.count(AuditLog.id))\
            .filter(AuditLog.timestamp >= cutoff_time)\
            .filter(AuditLog.severity.in_(['WARNING', 'ERROR', 'CRITICAL']))\
            .scalar()
        
        return {
            "total_actions": total_actions,
            "actions_by_type": [{"action": action, "count": count} for action, count in actions_by_type],
            "top_users": [{"user": user, "count": count} for user, count in top_users],
            "security_events": security_events,
            "time_period_hours": hours
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get audit stats: {str(e)}")
