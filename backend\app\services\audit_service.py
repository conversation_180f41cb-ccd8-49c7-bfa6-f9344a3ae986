from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from app.models.audit_log import <PERSON>t<PERSON>og
from app.schemas.audit_log import AuditLogCreate, AuditLogFilter
from typing import List, Optional, Dict, Any
from datetime import datetime
import json

class AuditService:
    def __init__(self, db: Session):
        self.db = db

    def log_action(self, audit_data: AuditLogCreate) -> AuditLog:
        """Log a user action to the audit trail"""
        try:
            # Convert details to JSON string if it's a dict
            details_json = audit_data.details
            if isinstance(details_json, dict):
                # Ensure all values are JSON serializable
                details_json = self._make_json_serializable(details_json)
            
            audit_log = AuditLog(
                user_id=audit_data.user_id,
                user_email=audit_data.user_email,
                action=audit_data.action.value,
                resource_type=audit_data.resource_type.value,
                resource_id=audit_data.resource_id,
                resource_name=audit_data.resource_name,
                details=details_json,
                ip_address=audit_data.ip_address,
                user_agent=audit_data.user_agent,
                session_id=audit_data.session_id,
                method=audit_data.method,
                endpoint=audit_data.endpoint,
                status_code=audit_data.status_code,
                response_time_ms=audit_data.response_time_ms,
                module=audit_data.module,
                feature=audit_data.feature,
                severity=audit_data.severity.value
            )
            
            self.db.add(audit_log)
            self.db.commit()
            self.db.refresh(audit_log)
            return audit_log
        except Exception as e:
            self.db.rollback()
            # Log the error but don't fail the main operation
            print(f"Failed to log audit action: {str(e)}")
            return None

    def get_audit_logs(self, filters: AuditLogFilter) -> List[AuditLog]:
        """Get audit logs with filtering"""
        query = self.db.query(AuditLog)
        
        # Apply filters
        conditions = []
        
        if filters.user_id:
            conditions.append(AuditLog.user_id == filters.user_id)
        
        if filters.user_email:
            conditions.append(AuditLog.user_email.ilike(f"%{filters.user_email}%"))
        
        if filters.action:
            conditions.append(AuditLog.action == filters.action.value)
        
        if filters.resource_type:
            conditions.append(AuditLog.resource_type == filters.resource_type.value)
        
        if filters.resource_id:
            conditions.append(AuditLog.resource_id == filters.resource_id)
        
        if filters.module:
            conditions.append(AuditLog.module == filters.module)
        
        if filters.feature:
            conditions.append(AuditLog.feature == filters.feature)
        
        if filters.severity:
            conditions.append(AuditLog.severity == filters.severity.value)
        
        if filters.start_date:
            conditions.append(AuditLog.timestamp >= filters.start_date)
        
        if filters.end_date:
            conditions.append(AuditLog.timestamp <= filters.end_date)
        
        if filters.ip_address:
            conditions.append(AuditLog.ip_address == filters.ip_address)
        
        if conditions:
            query = query.filter(and_(*conditions))
        
        # Order by timestamp descending (newest first)
        query = query.order_by(desc(AuditLog.timestamp))
        
        # Apply pagination
        query = query.offset(filters.offset).limit(filters.limit)
        
        return query.all()

    def get_user_activity(self, user_id: int, limit: int = 50) -> List[AuditLog]:
        """Get recent activity for a specific user"""
        return self.db.query(AuditLog)\
            .filter(AuditLog.user_id == user_id)\
            .order_by(desc(AuditLog.timestamp))\
            .limit(limit)\
            .all()

    def get_resource_history(self, resource_type: str, resource_id: str) -> List[AuditLog]:
        """Get history for a specific resource"""
        return self.db.query(AuditLog)\
            .filter(
                and_(
                    AuditLog.resource_type == resource_type,
                    AuditLog.resource_id == resource_id
                )
            )\
            .order_by(desc(AuditLog.timestamp))\
            .all()

    def get_security_events(self, hours: int = 24) -> List[AuditLog]:
        """Get security-related events in the last N hours"""
        from datetime import timedelta
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        security_actions = ['LOGIN', 'LOGOUT', 'CREATE', 'DELETE']
        
        return self.db.query(AuditLog)\
            .filter(
                and_(
                    AuditLog.timestamp >= cutoff_time,
                    or_(
                        AuditLog.action.in_(security_actions),
                        AuditLog.severity.in_(['WARNING', 'ERROR', 'CRITICAL'])
                    )
                )
            )\
            .order_by(desc(AuditLog.timestamp))\
            .all()

    def _make_json_serializable(self, obj: Any) -> Any:
        """Convert object to JSON serializable format"""
        if isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            return str(obj)
        else:
            return obj
