from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
from ..models.usecase import UsecaseStatus, ModuleType


class UsecaseBase(BaseModel):
    name: str
    description: Optional[str] = None
    module_type: ModuleType
    docker_image: str
    config_params: Optional[Dict[str, Any]] = None


class UsecaseCreate(UsecaseBase):
    pass


class UsecaseUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    docker_image: Optional[str] = None
    config_params: Optional[Dict[str, Any]] = None
    status: Optional[UsecaseStatus] = None


class UsecaseInDB(UsecaseBase):
    id: int
    status: UsecaseStatus
    endpoint_url: Optional[str] = None
    k8s_deployment_name: Optional[str] = None
    k8s_service_name: Optional[str] = None
    k8s_namespace: Optional[str] = None
    created_by_id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Usecase(UsecaseInDB):
    pass


class UsecaseDeployment(BaseModel):
    usecase_id: int
    config_params: Optional[Dict[str, Any]] = None


class UsecaseSandbox(BaseModel):
    usecase_id: int
    input_data: Dict[str, Any]
